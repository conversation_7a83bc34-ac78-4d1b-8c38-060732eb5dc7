'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode, useCallback } from 'react';
import { useAuth } from '@/lib/context/multi-user-auth-provider';
import { getAllStaff } from '@/lib/db/v4'; // knowledge: v4 direct staff access

// Define types for permissions
type PagePermissions = {
  menu: boolean;
  orders: boolean;
  waiter: boolean;
  kitchen: boolean;
  finance: boolean;
  analytics: boolean;
  inventory: boolean;
  production: boolean;
  staff: boolean;
  settings: boolean;
  suppliers: boolean;
};

type TabPermissions = {
  inventory?: {
    inventory?: boolean;
    counts?: boolean;
    waste?: boolean;
    production?: boolean;
    recettes?: boolean;
  };
  staff?: {
    staff?: boolean;
    shifts_schedule?: boolean;
    attendance?: boolean;
    payments?: boolean;
  };
  orders?: {
    collection?: boolean;
  };
};

type UserPermissions = {
  pages: PagePermissions;
  tabs: TabPermissions;
};

// Extended user type to include permissions
interface ExtendedUser {
  id: string;
  email: string;
  role: string;
  name?: string;
  permissions?: UserPermissions;
}

// Context type
interface PermissionContextType {
  isReady: boolean;
  permissions: UserPermissions;
  hasPageAccess: (page: keyof PagePermissions) => boolean;
  hasTabAccess: (page: string, tab: string) => boolean;
  isOwner: boolean;
  isAwaitingPermissions: boolean;
}

// Create context with default values
const PermissionContext = createContext<PermissionContextType>({
  isReady: false,
  permissions: {
    pages: {
      menu: false,
      orders: false,
      waiter: false,
      kitchen: false,
      finance: false,
      analytics: false,
      inventory: false,
      production: false,
      staff: false,
      settings: false,
      suppliers: false,
    },
    tabs: {
      inventory: {
        inventory: false,
        counts: false,
        waste: false,
        production: false,
        recettes: false,
      },
      staff: {
        staff: false,
        shifts_schedule: false,
        attendance: false,
        payments: false,
      },
      orders: {
        collection: false,
      }
    }
  },
  hasPageAccess: () => false,
  hasTabAccess: () => false,
  isOwner: false,
  isAwaitingPermissions: false,
});

// Define provider props
interface PermissionProviderProps {
  children: ReactNode;
}

// knowledge: Add a flag to disable all permissions checks for dev/owner
const DISABLE_PERMISSIONS = false; // Set to true to bypass all permission checks

export function PermissionProvider({ children }: PermissionProviderProps) {
  const { user, isAuthenticated } = useAuth();
  const [permissions, setPermissions] = useState<UserPermissions>({
    pages: {
      menu: false,
      orders: false,
      waiter: false,
      kitchen: false,
      finance: false,
      analytics: false,
      inventory: false,
      production: false,
      staff: false,
      settings: false,
      suppliers: false,
    },
    tabs: {
      inventory: {
        inventory: false,
        counts: false,
        waste: false,
        production: false,
        recettes: false,
      },
      staff: {
        staff: false,
        shifts_schedule: false,
        attendance: false,
        payments: false,
      }
    }
  });
  const [isReady, setIsReady] = useState(false);
  const [isOwner, setIsOwner] = useState(false);
  const [isAwaitingPermissions, setIsAwaitingPermissions] = useState(false);

  // Effect to load permissions when user changes
  useEffect(() => {
    const loadPermissions = async () => {
      if (DISABLE_PERMISSIONS) {
        setIsOwner(true);
        setPermissions({
          pages: {
            menu: true,
            orders: true,
            waiter: true,
            kitchen: true,
            finance: true,
            analytics: true,
            inventory: true,
            production: true,
            staff: true,
            settings: true,
            suppliers: true,
          },
          tabs: {
            inventory: {
              inventory: true,
              counts: true,
              waste: true,
              production: true,
              recettes: true,
            },
            staff: {
              staff: true,
              shifts_schedule: true,
              attendance: true,
              payments: true,
            },
            orders: {
              collection: true,
            }
          }
        });
        setIsAwaitingPermissions(false);
        setIsReady(true);
        return;
      }

      if (isAuthenticated && user) {
        const extendedUser = user as ExtendedUser;

        console.log('🔄 [Permissions useEffect] Auth state changed. Inspecting user object:', {
          userId: extendedUser.id,
          userName: extendedUser.name,
          userRole: extendedUser.role,
          userHasPermissionsField: 'permissions' in extendedUser,
          userPermissionsContent: extendedUser.permissions ? JSON.stringify(extendedUser.permissions, null, 2) : 'none'
        });

        // Check if user is owner
        if (extendedUser.role === 'owner' || extendedUser.role === 'admin') {
          setIsOwner(true);
          // Owners get access to everything
          setPermissions({
            pages: {
              menu: true,
              orders: true,
              waiter: true,
              kitchen: true,
              finance: true,
              analytics: true,
              inventory: true,
              production: true,
              staff: true,
              settings: true,
              suppliers: true,
            },
            tabs: {
              inventory: {
                inventory: true,
                counts: true,
                waste: true,
                production: true,
                recettes: true,
              },
              staff: {
                staff: true,
                shifts_schedule: true,
                attendance: true,
                payments: true,
              },
              orders: {
                collection: true,
              }
            }
          });
          setIsAwaitingPermissions(false);
          setIsReady(true);
          // Early return for owners/admins since they always have all permissions
          return;
        }
        
        setIsOwner(false);

        // For staff users, check if permissions exist in the user object (from token)
        if (extendedUser.permissions?.pages) {
          console.log('🔍 [Permissions] Found permissions in user object:', JSON.stringify(extendedUser.permissions, null, 2));
          
          // Handle the raw object format from PouchDB (this is the correct format)
          const pagesPermissions: PagePermissions = {
            menu: !!extendedUser.permissions.pages.menu,
            orders: !!extendedUser.permissions.pages.orders,
            waiter: !!extendedUser.permissions.pages.waiter,
            kitchen: !!extendedUser.permissions.pages.kitchen,
            finance: !!extendedUser.permissions.pages.finance,
            analytics: !!extendedUser.permissions.pages.analytics,
            inventory: !!extendedUser.permissions.pages.inventory,
            production: !!extendedUser.permissions.pages.production,
            staff: !!extendedUser.permissions.pages.staff,
            settings: !!extendedUser.permissions.pages.settings,
            suppliers: !!extendedUser.permissions.pages.suppliers,
          };
          
          // Use the permissions from the token, ensuring all values are boolean
          const staffPermissions = {
            pages: pagesPermissions,
            tabs: {
              inventory: {
                inventory: !!extendedUser.permissions.tabs?.inventory?.inventory,
                counts: !!extendedUser.permissions.tabs?.inventory?.counts,
                waste: !!extendedUser.permissions.tabs?.inventory?.waste,
                production: !!extendedUser.permissions.tabs?.inventory?.production,
                recettes: !!extendedUser.permissions.tabs?.inventory?.recettes,
              },
              staff: {
                staff: !!extendedUser.permissions.tabs?.staff?.staff,
                shifts_schedule: !!extendedUser.permissions.tabs?.staff?.shifts_schedule,
                attendance: !!extendedUser.permissions.tabs?.staff?.attendance,
                payments: !!extendedUser.permissions.tabs?.staff?.payments,
              }
            },
          };
          
          console.log('✅ [Permissions] Processed staff permissions:', JSON.stringify(staffPermissions, null, 2));
          setPermissions(staffPermissions);
          setIsAwaitingPermissions(false);
          setIsReady(true);
        } else {
          // Staff has no permissions yet, must sync to receive them
          setPermissions({
            pages: {
              menu: false,
              orders: false,
              waiter: false,
              kitchen: false,
              finance: false,
              analytics: false,
              inventory: false,
              production: false,
              staff: false,
              settings: false,
              suppliers: false,
            },
            tabs: {
              inventory: {
                inventory: false,
                counts: false,
                waste: false,
                production: false,
                recettes: false,
              },
              staff: {
                staff: false,
                shifts_schedule: false,
                attendance: false,
                payments: false,
              }
            },
          });
          setIsAwaitingPermissions(true);
          setIsReady(true);
        }
      } else {
        // Reset permissions when logged out
        setPermissions({
          pages: {
            menu: false,
            orders: false,
            waiter: false,
            kitchen: false,
            finance: false,
            analytics: false,
            inventory: false,
            production: false,
            staff: false,
            settings: false,
            suppliers: false,
          },
          tabs: {
            inventory: {
              inventory: false,
              counts: false,
              waste: false,
              production: false,
              recettes: false,
            },
            staff: {
              staff: false,
              shifts_schedule: false,
              attendance: false,
              payments: false,
            }
          }
        });
        setIsOwner(false);
        setIsAwaitingPermissions(false);
        setIsReady(true);
      }
    };

    loadPermissions();
  }, [isAuthenticated, user]);

  // Effect to subscribe to current user document changes in PouchDB
  useEffect(() => {
    let changesSubscription: any;

    const subscribeToUserChanges = async () => {
      if (!user || !isAuthenticated || isOwner) return; // Only for authenticated staff users

      // Import and initialize PouchDB dynamically
      const { initPouchDB } = await import('@/lib/db/pouchdb-init'); // knowledge: pouchdb import
      const PouchDB = await initPouchDB();

      if (!PouchDB) {
        console.error('❌ [Permissions] Failed to initialize PouchDB.');
        return;
      }

      // Assuming your database is named 'resto_data' and is accessed via the PouchDB instance
      const db = new PouchDB('resto_data'); // knowledge: use 'resto_data' db name

      const userId = user.id;
      const userDocId = `staff:${userId}`; // Assuming staff documents are prefixed with 'staff:'

      console.log(`👂 [Permissions] Subscribing to changes for user document: ${userDocId}`);

      changesSubscription = db.changes({
        since: 'now',
        live: true,
        include_docs: true,
        doc_ids: [userDocId] // Only subscribe to changes for the current user's document
      }).on('change', async (change: any) => {
        console.log(`📢 [Permissions] Change detected for user document ${userDocId}:`, change);
        if (change.doc && change.doc.permissions) {
          console.log('✅ [Permissions] User document updated with permissions, reloading...');
          // Permissions are now available, trigger a reload to update the app state
          window.location.reload();
        } else {
           console.log('🔬 [Permissions] User document changed, but no permissions found yet.');
        }
      }).on('error', (err: any) => {
        console.error(`❌ [Permissions] PouchDB changes subscription error for ${userDocId}:`, err);
        // Handle error, maybe attempt a refresh or show a message
      });
    };

    // Unsubscribe from previous changes when user or auth state changes
    if (changesSubscription) {
      console.log('✋ [Permissions] Unsubscribing from previous PouchDB changes.');
      changesSubscription.cancel();
    }

    subscribeToUserChanges();

    // Cleanup subscription on component unmount
    return () => {
      if (changesSubscription) {
        console.log('👋 [Permissions] Cleaning up PouchDB changes subscription.');
        changesSubscription.cancel();
      }
    };

  }, [user, isAuthenticated, isOwner]); // Dependencies

  // Helper function to check page access
  const hasPageAccess = useCallback((page: keyof PagePermissions): boolean => {
    // Owners always have access
    if (isOwner) return true;

    // For staff, check their specific permissions
    return permissions.pages[page];
  }, [permissions, isOwner]);

  // Helper function to check tab access
  const hasTabAccess = useCallback((page: string, tab: string): boolean => {
    // Owners always have access
    if (isOwner) return true;

    // First check if user has access to the parent page
    if (!permissions.pages[page as keyof PagePermissions]) {
      return false;
    }

    // If tabs permissions aren't defined, default to true for backward compatibility
    if (!permissions.tabs || !permissions.tabs[page as keyof TabPermissions]) {
      return true;
    }

    // Check specific tab permission
    const pageTabPermissions = permissions.tabs[page as keyof TabPermissions];
    if (!pageTabPermissions) {
      return true; // Default to true if tab permissions for this page aren't defined
    }

    // If the specific tab permission isn't defined, default to true
    return pageTabPermissions[tab as keyof typeof pageTabPermissions] ?? true;
  }, [permissions, isOwner]);

  // Context value
  const contextValue: PermissionContextType = {
    isReady,
    permissions,
    hasPageAccess,
    hasTabAccess,
    isOwner,
    isAwaitingPermissions,
  };

  return (
    <PermissionContext.Provider value={contextValue}>
      {children}
    </PermissionContext.Provider>
  );
}

// Hook to use permissions
export function usePermissions() {
  const context = useContext(PermissionContext);
  if (!context) {
    throw new Error('usePermissions must be used within a PermissionProvider');
  }
  return context;
}